<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>هناكل إيه بكرة؟</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow-x: hidden; /* Prevent horizontal scrolling */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        /* Basic styling for page visibility and transitions */
        .page {
            display: none;
            width: 100%;
            min-height: 100vh; /* Use min-height for full viewport height */
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 1rem;
            box-sizing: border-box;
            background-color: #f8f8f8;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }
        .page.active {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }
        .scrollable-content {
            width: 100%;
            max-width: 48rem; /* Max width for content */
            overflow-y: auto;
            flex-grow: 1; /* Allow content to grow */
            padding-bottom: 2rem; /* Space at bottom */
        }
        /* Custom scrollbar for better aesthetics */
        .scrollable-content::-webkit-scrollbar {
            width: 8px;
        }
        .scrollable-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .scrollable-content::-webkit-scrollbar-thumb {
            background: #a0aec0; /* Tailwind gray-400 */
            border-radius: 10px;
        }
        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: #718096; /* Tailwind gray-500 */
        }

        /* Modal specific styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
        }
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateY(20px);
            opacity: 0;
            transition: transform 0.3s ease-out, opacity 0.3s ease-out;
            position: relative;
        }
        .modal-overlay.active .modal-content {
            transform: translateY(0);
            opacity: 1;
        }
        .close-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            color: #4a5568; /* Tailwind gray-700 */
        }
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: #3b82f6; /* Tailwind blue-500 */
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .ai-response-area {
            white-space: pre-wrap; /* Preserves whitespace and newlines */
            word-wrap: break-word; /* Breaks long words */
            text-align: right; /* Ensure right-to-left alignment */
        }

        /* Enhanced Back Button Styles */
        .back-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
            z-index: 1500;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 30px rgba(59, 130, 246, 0.6);
            background: linear-gradient(135deg, #2563eb, #1e40af);
        }

        .back-button svg {
            width: 20px;
            height: 20px;
            transition: transform 0.3s ease;
        }

        .back-button:hover svg {
            transform: translateX(3px);
        }

        /* Hide default back buttons */
        .default-back-button {
            display: none;
        }

        /* Adjust page content to account for fixed header */
        .page-content {
            padding-top: 80px;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100 text-right">

    <!-- Home Page -->
    <div id="homePage" class="page active">
        <h1 class="text-5xl md:text-6xl font-extrabold text-gray-900 text-center mb-16 mt-24 leading-tight">
            <span class="block text-green-600">هناكل إيه بكرة؟</span>
            <span class="block text-lg mt-4 text-gray-600">تطبيقك لاختيار وجبتك المثالية</span>
        </h1>
        <div class="flex flex-col items-center justify-center w-full max-w-lg space-y-8">
            <button id="dietButton" class="w-full py-5 px-8 rounded-full bg-gradient-to-r from-green-500 to-green-700 text-white text-3xl font-bold shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <span>🥗</span>
                <span>هناكل دايت</span>
            </button>
            <button id="normalButton" class="w-full py-5 px-8 rounded-full bg-gradient-to-r from-amber-400 to-amber-600 text-white text-3xl font-bold shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <span>🍝</span>
                <span>هناكل عادي</span>
            </button>
            <button id="aboutAppButton" class="w-full py-5 px-8 rounded-full bg-indigo-500 text-white text-3xl font-bold shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <span>ℹ️</span>
                <span>عن التطبيق</span>
            </button>
        </div>
    </div>

    <!-- Category Page -->
    <div id="categoryPage" class="page">
        <button id="backToHomeButton" class="back-button">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span>الرئيسية</span>
        </button>
        <h2 id="categoryTitle" class="text-4xl font-bold text-gray-800 text-center mb-8 mt-20"></h2>

        <div class="scrollable-content">
            <div id="breakfastSection" class="bg-white rounded-2xl p-6 mb-8 shadow-lg">
                <h3 class="text-3xl font-bold text-gray-700 mb-6 text-right flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <span>فطار</span>
                    <span>☀️</span>
                </h3>
                <button data-meal-category="breakfast" class="suggest-meal-button bg-blue-500 text-white py-4 px-8 rounded-full text-xl font-semibold block mx-auto mb-6 shadow-md hover:bg-blue-600 transition-colors transform hover:scale-105">
                    اقتراح وجبة عشوائية 🎲
                </button>
                <!-- Suggestion Note -->
                <p id="breakfastSuggestionNote" class="suggestion-note text-center text-gray-500 text-sm mt-3 animate-pulse">
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة.
                </p>
                <div id="suggestedBreakfastMeal" class="suggested-meal-card hidden bg-blue-50 border border-blue-200 rounded-xl p-5 mt-4 flex-col sm:flex-row items-center justify-between shadow-inner">
                    <p class="meal-name text-2xl font-bold text-blue-800 flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    <button data-meal-category="breakfast" class="view-details-button bg-blue-600 text-white px-6 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-colors whitespace-nowrap shadow-md">
                        عرض التفاصيل
                    </button>
                </div>
            </div>

            <div id="lunchSection" class="bg-white rounded-2xl p-6 mb-8 shadow-lg">
                <h3 class="text-3xl font-bold text-gray-700 mb-6 text-right flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <span>غداء</span>
                    <span>🍜</span>
                </h3>
                <button data-meal-category="lunch" class="suggest-meal-button bg-blue-500 text-white py-4 px-8 rounded-full text-xl font-semibold block mx-auto mb-6 shadow-md hover:bg-blue-600 transition-colors transform hover:scale-105">
                    اقتراح وجبة عشوائية 🎲
                </button>
                <!-- Suggestion Note -->
                <p id="lunchSuggestionNote" class="suggestion-note text-center text-gray-500 text-sm mt-3 animate-pulse">
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة.
                </p>
                <div id="suggestedLunchMeal" class="suggested-meal-card hidden bg-blue-50 border border-blue-200 rounded-xl p-5 mt-4 flex-col sm:flex-row items-center justify-between shadow-inner">
                    <p class="meal-name text-2xl font-bold text-blue-800 flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    <button data-meal-category="lunch" class="view-details-button bg-blue-600 text-white px-6 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-colors whitespace-nowrap shadow-md">
                        عرض التفاصيل
                    </button>
                </div>
            </div>

            <div id="dinnerSection" class="bg-white rounded-2xl p-6 mb-8 shadow-lg">
                <h3 class="text-3xl font-bold text-gray-700 mb-6 text-right flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <span>عشاء</span>
                    <span>🌙</span>
                </h3>
                <button data-meal-category="dinner" class="suggest-meal-button bg-blue-500 text-white py-4 px-8 rounded-full text-xl font-semibold block mx-auto mb-6 shadow-md hover:bg-blue-600 transition-colors transform hover:scale-105">
                    اقتراح وجبة عشوائية 🎲
                </button>
                <!-- Suggestion Note -->
                <p id="dinnerSuggestionNote" class="suggestion-note text-center text-gray-500 text-sm mt-3 animate-pulse">
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة.
                </p>
                <div id="suggestedDinnerMeal" class="suggested-meal-card hidden bg-blue-50 border border-blue-200 rounded-xl p-5 mt-4 flex-col sm:flex-row items-center justify-between shadow-inner">
                    <p class="meal-name text-2xl font-bold text-blue-800 flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    <button data-meal-category="dinner" class="view-details-button bg-blue-600 text-white px-6 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-colors whitespace-nowrap shadow-md">
                        عرض التفاصيل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Detail Page -->
    <div id="mealDetailPage" class="page">
        <button id="backToCategoryButton" class="back-button">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span>الوجبات</span>
        </button>
        <h2 id="mealDetailTitle" class="text-4xl font-bold text-gray-800 text-center mb-8 mt-20"></h2>

        <div class="scrollable-content">
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>التفاصيل</span>
                    <span>📝</span>
                </h3>
                <p id="mealDetails" class="text-lg text-gray-600 leading-relaxed text-right"></p>
            </div>

            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>المكونات</span>
                    <span>🍎</span>
                </h3>
                <ul id="mealIngredients" class="list-none p-0 m-0"></ul>
            </div>

            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>طريقة التحضير</span>
                    <span>👨‍🍳</span>
                </h3>
                <p id="mealInstructions" class="text-lg text-gray-600 leading-relaxed text-right"></p>
            </div>

            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>السعرات الحرارية</span>
                    <span>🔥</span>
                </h3>
                <p id="mealCalories" class="text-lg text-gray-600 leading-relaxed text-right"></p>
            </div>

            <!-- AI Feature Section -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>تعديل الوصفة بالذكاء الاصطناعي</span>
                    <span>✨</span>
                </h3>
                <button id="aiModifyRecipeButton" class="w-full bg-purple-600 text-white py-3 px-6 rounded-full text-xl font-semibold shadow-md hover:bg-purple-700 transition-colors transform hover:scale-105">
                    ✨ تعديل الوصفة بالذكاء الاصطناعي ✨
                </button>
            </div>
        </div>
    </div>

    <!-- About App Page -->
    <div id="aboutAppPage" class="page">
        <button id="backFromAboutButton" class="back-button">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span>الرئيسية</span>
        </button>
        <h2 class="text-4xl font-bold text-gray-800 text-center mb-8 mt-20">عن تطبيق "هناكل إيه بكرة؟"</h2>

        <div class="scrollable-content bg-white rounded-2xl p-6 shadow-lg">
            <p class="text-lg text-gray-700 leading-relaxed mb-4 text-right">
                تطبيق "هناكل إيه بكرة؟" هو رفيقك اليومي لاختيار وجباتك بمنتهى السهولة والمرونة! سواء كنت تتبع حمية غذائية صارمة (دايت) أو تفضل الوجبات العادية، يوفر لك التطبيق مجموعة واسعة من الاقتراحات اللذيذة والمغذية.
            </p>
            <h3 class="text-2xl font-bold text-gray-700 mb-3 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                <span>الميزات الرئيسية</span>
                <span>🌟</span>
            </h3>
            <ul class="list-none p-0 m-0 mb-4">
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">اقتراحات وجبات متنوعة:</span> اختر بين وجبات "دايت" أو "عادية" لفطارك، غدائك، وعشائك، مع أكثر من 200 وجبة مختلفة لكل خيار.</li>
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">تفاصيل وصفات كاملة:</span> لكل وجبة، ستجد تفاصيلها، مكوناتها الدقيقة، طريقة التحضير خطوة بخطوة، وحساب تقديري للسعرات الحرارية لكل 100 جرام وحجم الوجبة.</li>
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">تعديل الوصفات بالذكاء الاصطناعي:</span> هذه الميزة الثورية تسمح لك بتخصيص أي وصفة باستخدام **الذكاء الاصطناعي**! فقط اطلب من الذكاء الاصطناعي تعديل الوصفة (مثال: "اجعلها نباتية"، "أضف المزيد من البروتين"، "قلل السعرات الحرارية") وسيقوم بإعادة صياغة الوصفة لك.</li>
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">تصميم احترافي وسهل الاستخدام:</span> واجهة جذابة، أيقونات واضحة، وتجربة مستخدم سلسة تجعل تصفح الوجبات أمراً ممتعاً.</li>
            </ul>
            <p class="text-lg text-gray-700 leading-relaxed text-right">
                نتمنى لك تجربة ممتعة ومفيدة مع تطبيق "هناكل إيه بكرة؟".
            </p>
        </div>
    </div>


    <!-- AI Modification Modal -->
    <div id="aiModal" class="modal-overlay hidden">
        <div class="modal-content">
            <span class="close-button" id="closeModalButton">&times;</span>
            <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right">عدّل وصفتك بالذكاء الاصطناعي</h3>
            <textarea id="aiPromptInput" class="w-full p-3 border border-gray-300 rounded-lg mb-4 text-right" rows="4" placeholder="مثال: اجعلها نباتية، أضف المزيد من البروتين، قلل السعرات الحرارية، اجعلها خالية من الجلوتين..."></textarea>
            <button id="sendPromptButton" class="w-full bg-blue-500 text-white py-3 px-6 rounded-full text-lg font-semibold shadow-md hover:bg-blue-600 transition-colors mb-4">
                أرسل الطلب
            </button>
            <div id="aiLoadingIndicator" class="flex justify-center items-center py-4 hidden">
                <div class="loading-spinner"></div>
                <span class="mr-3 text-gray-700">الذكاء الاصطناعي يفكر...</span>
            </div>
            <div id="aiResponseArea" class="bg-gray-50 p-4 rounded-lg border border-gray-200 text-gray-800 text-lg ai-response-area min-h-[100px] max-h-[300px] overflow-y-auto">
                <!-- AI response will be displayed here -->
            </div>
             <p id="aiErrorDisplay" class="text-red-600 text-sm mt-2 hidden text-right"></p>
        </div>
    </div>

    <script>
        // --- Lists of specific meal names for better variety ---
        const dietBreakfastNames = [
            "شوفان بالتوت واللوز", "بيض مسلوق مع سبانخ وطماطم", "زبادي يوناني مع الفاكهة والعسل", "ساندويتش جبنة قريش بالخيار",
            "سموثي أخضر بالكيوي والسبانخ", "توست أفوكادو بالبيض المسلوق", "فطائر الشوفان الصحية بالتفاح", "جرانولا منزلية بالفواكه المجففة",
            "سموثي البروتين بالموز وزبدة الفول السوداني", "فول مدمس خفيف بزيت الزيتون والكمون", "بان كيك الشوفان", "أومليت الخضروات"
        ];
        const dietLunchNames = [
            "سلطة دجاج مشوي بالخضروات الورقية", "سمك سلمون مشوي مع أرز بسمتي بني", "شوربة عدس بالخضروات المشكلة", "خضار سوتيه وصدر دجاج بالليمون",
            "سلطة الكينوا بالخضروات والحمص", "تونا بالخضروات الطازجة وصوص الزبادي", "رول الخس بالدجاج والخضار", "عدس بجبنة البارميزان الخفيفة",
            "بوريتو بول صحي بالدجاج والأرز البري", "خضروات مشوية وحمص بالليمون والثوم", "شوربة الطماطم بالريحان", "فيليه دجاج مشوي"
        ];
        const dietDinnerNames = [
            "جبنة قريش بالخضروات المقطعة وزيت الزيتون", "سلطة تونة لايت بالذرة والفلفل", "صدر دجاج مسلوق مع بروكلي على البخار", "شوربة بروكلي بالكريمة الخفيفة",
            "بيض أومليت خفيف بالسبانخ والفطر", "زبادي وخيار بالنعناع", "خضروات مشوية خفيفة بالبهارات", "سمك فيليه على البخار مع الليمون",
            "سلطة فواكه بالزبادي قليل الدسم", "دجاج مسلوق وخضروات مسلوقة", "سلطة يونانية خفيفة", "شوربة الخضار بالكريمة"
        ];

        const normalBreakfastNames = [
            "فول بالزيت والليمون مع البيض المدحرج", "بيض بالبسطرمة والجبنة الموتزاريلا", "مناقيش جبنة وزعتر طازجة من الفرن", "شكشوكة بالبيض والطماطم والفلفل",
            "ساندويتشات الجبن الساخن بالزبدة", "فطائر باللحمة المفرومة والبصل", "خبز بالبيض والجبن الشيدر", "عجة البيض بالخضروات والبهارات",
            "حواوشي فطار باللحم المفروم والخبز البلدي", "بيض بالنقانق والبطاطس المقلية", "طعمية سخنة", "مربى وقشطة وعسل"
        ];
        const normalLunchNames = [
            "مكرونة بالبشاميل واللحم المفروم الغني", "محاشي مشكلة (كوسة، باذنجان، فلفل) بالأرز والخلطة السرية", "أرز بالخضار وقطع الدجاج المتبلة", "كشري مصري بجميع مكوناته",
            "فتة شاورما الدجاج بالثومية والعيش المقلي", "كفتة بالطحينة وصوص الطماطم", "طواجن لحمة بالبطاطس في الفرن", "مبكبكة باللحم المتبل والمكرونة",
            "أرز معمر باللحم والزبدة", "صيادية السمك البوري بالصوص البني", "كبدة إسكندراني بالردة", "فته كوارع"
        ];
        const normalDinnerNames = [
            "حواوشي إسكندراني بالجبنة", "بطاطس محمرة وبرجر لحم بالجبنة", "كشري حلة واحدة سريع ولذيذ", "فطير مشلتت بالعسل الأسود والطحينة",
            "صينية دجاج بالبطاطس والجزر في الفرن", "شوربة سي فود بالكريمة والمأكولات البحرية", "معكرونة بالصلصة الحمراء واللحم المفروم", "بيتزا منزلية بالخضروات والجبن",
            "شيش طاووق على الفحم مع الأرز المبهر", "ساندويتشات كبدة بالعيش الفينو", "كفتة الأرز بالصلصة", "ساندويتشات التونة"
        ];

        // --- Dummy Data Generation (More than 200 meals) ---
        const generateMeals = (nameList, count, isDiet, type) => {
            const meals = [];
            for (let i = 0; i < count; i++) {
                let baseMealName = nameList[i % nameList.length];
                let name = baseMealName;
                if (count > nameList.length) {
                    name += ` (${Math.floor(i / nameList.length) + 1})`;
                }

                let details = `وجبة ${isDiet ? 'صحية ' : 'شهية '} ولذيذة، مثالية لوجبة ${type}. يمكنك الاستمتاع بها كخيار سريع ومغذي.`;
                let ingredients = [];
                let instructions = ''; // Initialize instructions
                let caloriesPer100g;
                let servingSizeGrams;

                // Assign ingredients based on baseMealName and type
                // Default ingredients (will be overridden by specific ones)
                if (type === 'breakfast') {
                    ingredients = ["مكون أساسي (حبوب/بيض)", "خضروات/فواكه طازجة", "مصدر بروتين خفيف"];
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (200 - 150 + 1)) + 150 : Math.floor(Math.random() * (250 - 200 + 1)) + 200;
                    servingSizeGrams = Math.floor(Math.random() * (200 - 150 + 1)) + 150;
                } else if (type === 'lunch') {
                    ingredients = ["مصدر بروتين (دجاج/لحم/سمك)", "كربوهيدرات معقدة (أرز بني/كينوا/خضار)", "خضروات طازجة متنوعة"];
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (250 - 200 + 1)) + 200 : Math.floor(Math.random() * (350 - 300 + 1)) + 300;
                    servingSizeGrams = Math.floor(Math.random() * (300 - 200 + 1)) + 200;
                } else if (type === 'dinner') {
                    ingredients = ["خضروات خفيفة (مطبوخة/سلطة)", "بروتين قليل الدسم (جبن/زبادي/دجاج)", "مصدر ألياف (خبز أسمر/بقوليات)"];
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (180 - 120 + 1)) + 120 : Math.floor(Math.random() * (280 - 220 + 1)) + 220;
                    servingSizeGrams = Math.floor(Math.random() * (250 - 150 + 1)) + 150;
                }

                // Override with specific ingredients and instructions based on common meal names
                if (baseMealName.includes('شوفان')) {
                    ingredients = ["1/2 كوب شوفان", "1 كوب ماء/حليب قليل الدسم", "1/2 كوب توت مشكل", "ملعقة صغيرة عسل/مكسرات"];
                    instructions = `لتحضير ${name}:
1. اخلط نصف كوب شوفان مع كوب ماء أو حليب قليل الدسم في قدر.
2. سخّن على نار متوسطة مع التحريك المستمر حتى يتكثف.
3. أضف التوت والمكسرات (مثل اللوز) وملعقة صغيرة من العسل أو بديل السكر.
4. قدّمها ساخنة واستمتع بوجبة فطار غنية بالألياف.`;
                    caloriesPer100g = isDiet ? 389 : 420; // Example for oatmeal
                    servingSizeGrams = Math.floor(Math.random() * (200 - 150 + 1)) + 150; // 150-200g
                } else if (baseMealName.includes('بيض مسلوق')) {
                    ingredients = ["2 بيضة مسلوقة", "1 كوب سبانخ طازجة", "1 طماطم متوسطة"];
                    instructions = `لتحضير ${name}:
1. اسلق البيض لمدة 7-10 دقائق للحصول على بيض مسلوق صلب.
2. قطّع السبانخ الطازجة والطماطم إلى قطع صغيرة.
3. قشّر البيض وقطّعه إلى أنصاف أو أرباع.
4. اخلط المكونات مع رشة ملح وفلفل، وقدمها كوجبة فطار خفيفة ومغذية.`;
                    caloriesPer100g = isDiet ? 155 : 180; // Example for boiled eggs
                    servingSizeGrams = Math.floor(Math.random() * (150 - 100 + 1)) + 100; // 100-150g
                } else if (baseMealName.includes('زبادي')) {
                    ingredients = ["1 كوب زبادي يوناني قليل الدسم", "1/2 كوب فراولة مقطعة", "1/4 كوب كيوي مقطع", "ملعقة صغيرة بذور شيا"];
                    instructions = `لتحضير ${name}:
1. ضع الزبادي في وعاء.
2. أضف الفاكهة المقطعة فوق الزبادي.
3. رش بذور الشيا (اختياري).
4. استمتع بوجبة خفيفة ومنعشة.`;
                    caloriesPer100g = isDiet ? 60 : 80; // Example for yogurt
                    servingSizeGrams = Math.floor(Math.random() * (250 - 200 + 1)) + 200;
                } else if (baseMealName.includes('سلطة دجاج مشوي')) {
                    ingredients = ["150 جرام صدر دجاج مشوي", "2 كوب خس متنوع", "1 طماطم كبيرة", "1/2 خيار", "صلصة سلطة لايت"];
                    instructions = `لتحضير ${name}:
1. اقطع صدر الدجاج إلى مكعبات واشويه حتى ينضج.
2. اخلط الخس، الطماطم الكرزية، الخيار، والفلفل الألوان في وعاء كبير.
3. أضف قطع الدجاج المشوي إلى السلطة.
4. حضّر صلصة لايت من زيت الزيتون، عصير الليمون، والخل البلسمي.
5. اسكب الصلصة فوق السلطة وقلّب جيداً وقدمها فوراً.`;
                    caloriesPer100g = isDiet ? 120 : 150; // Example for chicken salad
                    servingSizeGrams = Math.floor(Math.random() * (400 - 300 + 1)) + 300;
                } else if (baseMealName.includes('سمك') && type === 'lunch') {
                    ingredients = ["150 جرام فيليه سمك", "1/2 كوب أرز بني مطبوخ", "1 كوب بروكلي مطبوخ", "شريحة ليمون"];
                    instructions = `لتحضير ${name}:
1. اشوِ فيليه السمك في الفرن أو على الشواية حتى ينضج.
2. اسلق الأرز البني والبروكلي.
3. قدّم السمك مع الأرز والبروكلي، وزين بشريحة ليمون.`;
                    caloriesPer100g = isDiet ? 140 : 170; // Example for fish & brown rice
                    servingSizeGrams = Math.floor(Math.random() * (350 - 250 + 1)) + 250;
                } else if (baseMealName.includes('مكرونة بالبشاميل')) {
                    ingredients = ["200 جرام مكرونة قلم", "250 جرام لحم مفروم", "500 مل بشاميل", "جبنة موتزاريلا"];
                    instructions = `لتحضير ${name}:
1. اسلق المكرونة القلم نصف سلقة.
2. حضّر اللحم المفروم المعصّج بالبصل والبهارات.
3. حضّر صلصة البشاميل: قم بإذابة الزبدة، أضف الدقيق ثم الحليب تدريجياً مع التحريك المستمر حتى يتكثف.
4. رص طبقات من المكرونة، ثم اللحم، ثم البشاميل في صينية.
5. أدخلها الفرن حتى يصبح الوجه ذهبياً وقدمها ساخنة.`;
                    caloriesPer100g = isDiet ? 200 : 280; // Example for Bechamel
                    servingSizeGrams = Math.floor(Math.random() * (500 - 400 + 1)) + 400;
                } else if (baseMealName.includes('جبنة قريش') && type === 'dinner') {
                    ingredients = ["100 جرام جبنة قريش", "1 طماطم صغيرة", "1/2 فلفل أخضر", "رشة زيت زيتون وزعتر"];
                    instructions = `لتحضير ${name}:
1. ضع الجبنة القريش في طبق.
2. قطّع الخيار والطماطم والفلفل الأخضر إلى مكعبات صغيرة.
3. أضف الخضروات المقطعة فوق الجبنة.
4. رش قليل من زيت الزيتون والزعتر حسب الرغبة.
5. قدمها كوجبة عشاء خفيفة وسريعة.`;
                    caloriesPer100g = isDiet ? 98 : 120; // Example for cottage cheese
                    servingSizeGrams = Math.floor(Math.random() * (180 - 120 + 1)) + 120;
                } else if (baseMealName.includes('حواوشي') && type === 'dinner') {
                    ingredients = ["1 رغيف خبز بلدي طازج", "150 جرام لحم مفروم طازج", "1 بصلة متوسطة مفرومة ناعماً", "1/2 فلفل أخضر حلو مفروم", "2 فص ثوم مفروم", "1 ملعقة صغيرة بهارات حواوشي", "1/2 ملعقة صغيرة ملح", "1/4 ملعقة صغيرة فلفل أسود", "2 ملعقة كبيرة زيت للدهن"];
                    instructions = `🥙 طريقة تحضير ${name}:

⏰ وقت التحضير: 15 دقيقة | وقت الطبخ: 20 دقيقة | إجمالي: 35 دقيقة

📋 خطوات التحضير:
1️⃣ تحضير الحشوة:
   • في وعاء كبير، اخلط اللحم المفروم مع البصل المفروم
   • أضف الفلفل الأخضر والثوم المفروم
   • تبّل بالملح والفلفل الأسود وبهارات الحواوشي
   • اخلط جيداً بالأيدي حتى تتجانس المكونات
   • اتركها ترتاح 10 دقائق لتتشرب النكهات

2️⃣ تحضير الخبز:
   • اقطع الخبز البلدي من المنتصف بحذر (لا تقطعه كاملاً)
   • افتح الخبز برفق لتكوين جيب
   • تأكد من عدم تمزق الخبز أثناء الفتح

3️⃣ الحشو:
   • وزّع خليط اللحم بالتساوي داخل الخبز
   • اضغط برفق لتوزيع الحشوة في جميع الأنحاء
   • تأكد من وصول الحشوة للأطراف
   • أغلق الخبز واضغط عليه برفق

4️⃣ الطبخ:
   • سخّن الفرن على 200°م
   • ادهن الخبز من الخارج بالزيت من جميع الجهات
   • ضعه في صينية فرن مدهونة
   • اخبز لمدة 15-20 دقيقة مع التقليب مرة واحدة
   • تأكد من نضج اللحم وتحمر الخبز

5️⃣ التقديم:
   • اتركه يبرد قليلاً لمدة 3-5 دقائق
   • اقطعه بسكين حاد إلى قطع متساوية
   • قدّمه ساخناً مع السلطة والمخلل

💡 نصائح الشيف:
   • تأكد من أن اللحم طازج وخالي من الدهون الزائدة
   • يمكن إضافة البقدونس المفروم للحشوة
   • لا تفرط في الحشو حتى لا ينفجر الخبز`;
                    caloriesPer100g = isDiet ? 250 : 350;
                    servingSizeGrams = Math.floor(Math.random() * (300 - 200 + 1)) + 200;
                } else {
                    // Fallback for instructions if no specific match
                    instructions = `🍽️ طريقة تحضير ${name}:

⏰ وقت التحضير: 10-15 دقيقة | وقت الطبخ: 15-25 دقيقة | إجمالي: 25-40 دقيقة

📋 خطوات التحضير العامة:
1️⃣ التحضير:
   • اجمع جميع المكونات المطلوبة
   • اغسل الخضروات والفواكه جيداً
   • حضّر أدوات الطبخ اللازمة

2️⃣ الطبخ:
   • اتبع طريقة الطهي المناسبة للوجبة:
     - للشواء: سخّن الشواية أو المقلاة على نار متوسطة-عالية
     - للسلق: استخدم ماء مغلي مملح
     - للقلي: سخّن الزيت على نار متوسطة
     - للخبز: سخّن الفرن على 180-200°م

3️⃣ التتبيل:
   • أضف الملح والفلفل والبهارات حسب الذوق
   • تذوق واضبط النكهة حسب الرغبة

4️⃣ التقديم:
   • قدّم الوجبة ساخنة في أطباق مناسبة
   • أضف طبق جانبي من السلطة أو الخضروات
   • زيّن بالأعشاب الطازجة إذا رغبت

💡 نصائح عامة:
   • استخدم مكونات طازجة للحصول على أفضل نكهة
   • لا تفرط في الطبخ للحفاظ على القيمة الغذائية
   • يمكن تعديل الكميات حسب عدد الأشخاص`;
                }

                // Calculate total calories for the serving
                const totalCalories = Math.round((caloriesPer100g / 100) * servingSizeGrams);
                const calorieString = `إجمالي السعرات الحرارية: ${totalCalories} سعرة حرارية (لكل 100 جرام: ${caloriesPer100g} سعرة حرارية، حجم الوجبة: ${servingSizeGrams} جرام)`;

                meals.push({ id: `${baseMealName.replace(/\s/g, '')}_${i}`, name, details, ingredients, instructions, calories: calorieString });
            }
            return meals;
        };

        const NUM_MEALS_PER_CATEGORY_TYPE = 70; // This will give 70 unique IDs per category

        const dietMeals = {
            breakfast: generateMeals(dietBreakfastNames, NUM_MEALS_PER_CATEGORY_TYPE, true, 'breakfast'),
            lunch: generateMeals(dietLunchNames, NUM_MEALS_PER_CATEGORY_TYPE, true, 'lunch'),
            dinner: generateMeals(dietDinnerNames, NUM_MEALS_PER_CATEGORY_TYPE, true, 'dinner'),
        };

        const normalMeals = {
            breakfast: generateMeals(normalBreakfastNames, NUM_MEALS_PER_CATEGORY_TYPE, false, 'breakfast'),
            lunch: generateMeals(normalLunchNames, NUM_MEALS_PER_CATEGORY_TYPE, false, 'lunch'),
            dinner: generateMeals(normalDinnerNames, NUM_MEALS_PER_CATEGORY_TYPE, false, 'dinner'),
        };
        // --- End Dummy Data Generation ---

        // All main logic and event listeners are now defined inside window.onload
        window.onload = () => {
            // Helper function to get a random meal (now defined within onload scope)
            const getRandomMeal = (mealType, dietType) => {
                const meals = dietType === 'diet' ? dietMeals : normalMeals;
                const categoryMeals = meals[mealType];
                const randomIndex = Math.floor(Math.random() * categoryMeals.length);
                return categoryMeals[randomIndex];
            };

            // Global state variables (now defined within onload scope)
            let currentDietType = '';
            // Store the currently suggested meal objects by their category, using meal.id as the key
            let currentSuggestedMealObjects = {
                breakfast: null,
                lunch: null,
                dinner: null,
            };
            let currentMealBeingViewed = null; // To store the meal object shown on detail page

            // DOM Elements (now retrieved within onload scope)
            const homePage = document.getElementById('homePage');
            const categoryPage = document.getElementById('categoryPage');
            const mealDetailPage = document.getElementById('mealDetailPage');
            const aboutAppPage = document.getElementById('aboutAppPage'); // New about app page

            const aiModal = document.getElementById('aiModal');
            const aiPromptInput = document.getElementById('aiPromptInput');
            const sendPromptButton = document.getElementById('sendPromptButton');
            const aiLoadingIndicator = document.getElementById('aiLoadingIndicator');
            const aiResponseArea = document.getElementById('aiResponseArea');
            const aiErrorDisplay = document.getElementById('aiErrorDisplay');

            const dietButton = document.getElementById('dietButton');
            const normalButton = document.getElementById('normalButton');
            const aboutAppButton = document.getElementById('aboutAppButton'); // New about app button
            const backToHomeButton = document.getElementById('backToHomeButton');
            const backToCategoryButton = document.getElementById('backToCategoryButton');
            const backFromAboutButton = document.getElementById('backFromAboutButton'); // New back from about button

            const categoryTitle = document.getElementById('categoryTitle');
            const suggestMealButtons = document.querySelectorAll('.suggest-meal-button');
            const suggestedMealCards = {
                breakfast: document.getElementById('suggestedBreakfastMeal'),
                lunch: document.getElementById('suggestedLunchMeal'),
                dinner: document.getElementById('suggestedDinnerMeal')
            };
            // Get all view-details-buttons
            const viewDetailsButtons = document.querySelectorAll('.view-details-button');

            const mealDetailTitle = document.getElementById('mealDetailTitle');
            const mealDetails = document.getElementById('mealDetails');
            const mealIngredients = document.getElementById('mealIngredients');
            const mealInstructions = document.getElementById('mealInstructions');
            const mealCalories = document.getElementById('mealCalories');

            // Get suggestion notes
            const breakfastSuggestionNote = document.getElementById('breakfastSuggestionNote');
            const lunchSuggestionNote = document.getElementById('lunchSuggestionNote');
            const dinnerSuggestionNote = document.getElementById('dinnerSuggestionNote');
            const allSuggestionNotes = {
                breakfast: breakfastSuggestionNote,
                lunch: lunchSuggestionNote,
                dinner: dinnerSuggestionNote
            };


            // Navigation Functions (now defined within onload scope)
            const showPage = (pageId) => {
                homePage.classList.remove('active');
                categoryPage.classList.remove('active');
                mealDetailPage.classList.remove('active');
                aboutAppPage.classList.remove('active'); // Remove active from new about page

                document.getElementById(pageId).classList.add('active');
                window.scrollTo(0, 0);
            };

            const navigateToCategory = (dietType) => {
                currentDietType = dietType;
                categoryTitle.textContent = dietType === 'diet' ? 'وجبات الدايت' : 'وجبات عادية';

                currentSuggestedMealObjects = { breakfast: null, lunch: null, dinner: null };
                const allSuggestedMealCards = document.querySelectorAll('.suggested-meal-card');
                allSuggestedMealCards.forEach(card => {
                    card.classList.add('hidden');
                    card.style.display = 'none';
                });

                // Show all suggestion notes when entering category page
                Object.values(allSuggestionNotes).forEach(note => {
                    if (note) { // Check if element exists
                        note.classList.remove('hidden');
                        note.style.display = 'block'; // Ensure it's displayed
                    }
                });

                showPage('categoryPage');
            };

            const navigateToMealDetail = (meal) => {
                currentMealBeingViewed = meal; // Store the meal object for AI modification
                mealDetailTitle.textContent = meal.name;
                mealDetails.textContent = meal.details;
                mealInstructions.textContent = meal.instructions;
                mealCalories.textContent = meal.calories;

                mealIngredients.innerHTML = '';
                meal.ingredients.forEach(item => {
                    const li = document.createElement('li');
                    li.className = 'text-lg text-gray-600 leading-relaxed mb-1 text-right';
                    li.textContent = `• ${item}`;
                    mealIngredients.appendChild(li);
                });
                showPage('mealDetailPage');
            };

            // Function to show/hide AI modal
            const toggleAiModal = (show) => {
                if (show) {
                    aiModal.classList.add('active');
                    aiModal.classList.remove('hidden');
                    aiPromptInput.value = ''; // Clear previous input
                    aiResponseArea.textContent = ''; // Clear previous response
                    aiErrorDisplay.classList.add('hidden'); // Hide error
                } else {
                    aiModal.classList.remove('active');
                    // Add hidden class after transition completes
                    setTimeout(() => {
                        aiModal.classList.add('hidden');
                    }, 300);
                }
            };

            // --- Gemini API Call Function ---
            const callGeminiAPI = async (prompt) => {
                aiLoadingIndicator.classList.remove('hidden');
                aiResponseArea.textContent = ''; // Clear previous response
                aiErrorDisplay.classList.add('hidden'); // Hide any previous errors

                const apiKey = ""; // API key will be provided by Canvas runtime
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

                let chatHistory = [];
                chatHistory.push({ role: "user", parts: [{ text: prompt }] });
                const payload = { contents: chatHistory };

                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Gemini API Error: ${response.status} - ${errorData.error.message || 'Unknown error'}`);
                    }

                    const result = await response.json();
                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        const text = result.candidates[0].content.parts[0].text;
                        aiResponseArea.textContent = text;
                    } else {
                        throw new Error("Gemini API did not return expected content.");
                    }
                } catch (error) {
                    console.error("Error calling Gemini API:", error);
                    aiResponseArea.textContent = "عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.";
                    aiErrorDisplay.textContent = `خطأ: ${error.message}`;
                    aiErrorDisplay.classList.remove('hidden');
                } finally {
                    aiLoadingIndicator.classList.add('hidden');
                }
            };


            // Attach Event Listeners
            dietButton.addEventListener('click', () => navigateToCategory('diet'));
            normalButton.addEventListener('click', () => navigateToCategory('normal'));
            aboutAppButton.addEventListener('click', () => showPage('aboutAppPage')); // Event listener for new button

            backToHomeButton.addEventListener('click', () => showPage('homePage'));
            backToCategoryButton.addEventListener('click', () => navigateToCategory(currentDietType));
            backFromAboutButton.addEventListener('click', () => showPage('homePage')); // Event listener for new back button

            suggestMealButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    const mealCategory = event.target.dataset.mealCategory;
                    console.log(`[DEBUG] زر الاقتراح ${mealCategory} تم الضغط عليه.`);

                    const meal = getRandomMeal(mealCategory, currentDietType);
                    currentSuggestedMealObjects[mealCategory] = meal;
                    console.log(`[DEBUG] تم اقتراح وجبة:`, meal);

                    const cardElement = suggestedMealCards[mealCategory];
                    const suggestionNoteElement = allSuggestionNotes[mealCategory];

                    if (cardElement && meal) {
                        const mealNameElement = cardElement.querySelector('.meal-name');
                        if (mealNameElement) {
                            mealNameElement.textContent = meal.name;
                            console.log(`[DEBUG] تم تحديث اسم الوجبة إلى: ${meal.name}`);
                        } else {
                            console.error(`[ERROR] لم يتم العثور على عنصر اسم الوجبة داخل البطاقة: ${mealCategory}`);
                        }

                        cardElement.classList.remove('hidden');
                        cardElement.style.display = 'flex';
                        console.log(`[DEBUG] تم إظهار بطاقة الوجبة المقترحة لـ: ${mealCategory}`);

                        // Hide the suggestion note once a meal is suggested
                        if (suggestionNoteElement) {
                            suggestionNoteElement.classList.add('hidden');
                            suggestionNoteElement.style.display = 'none';
                        }

                    } else {
                        console.error(`[ERROR] بطاقة الوجبة المقترحة أو الوجبة المقترحة غير موجودة لـ: ${mealCategory}`);
                    }
                });
            });

            viewDetailsButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    const mealCategory = event.target.dataset.mealCategory;
                    const mealToDisplay = currentSuggestedMealObjects[mealCategory];

                    if (mealToDisplay) {
                        navigateToMealDetail(mealToDisplay);
                    } else {
                        console.warn(`[WARNING] لا توجد وجبة مقترحة مخزنة لـ ${mealCategory} عند محاولة عرض التفاصيل. يتم اقتراح وجبة عشوائية بدلاً من ذلك.`);
                        const fallbackMeal = getRandomMeal(mealCategory, currentDietType);
                        navigateToMealDetail(fallbackMeal);
                    }
                });
            });

            // AI Feature Event Listeners
            aiModifyRecipeButton.addEventListener('click', () => {
                if (currentMealBeingViewed) {
                    toggleAiModal(true);
                } else {
                    // This case should ideally not happen if user navigates correctly
                    alert("يرجى اختيار وجبة أولاً لعرض تفاصيلها.");
                }
            });

            closeModalButton.addEventListener('click', () => {
                toggleAiModal(false);
            });

            sendPromptButton.addEventListener('click', () => {
                const userPrompt = aiPromptInput.value.trim();
                if (!userPrompt) {
                    aiErrorDisplay.textContent = "الرجاء إدخال طلبك.";
                    aiErrorDisplay.classList.remove('hidden');
                    return;
                }

                if (!currentMealBeingViewed) {
                    aiResponseArea.textContent = "عذراً، لا توجد وجبة حالية لتعديلها. يرجى العودة واختيار وجبة.";
                    aiErrorDisplay.classList.remove('hidden');
                    return;
                }

                // Construct a detailed prompt for Gemini
                const mealDetailsForAI = `الاسم: ${currentMealBeingViewed.name}
التفاصيل: ${currentMealBeingViewed.details}
المكونات: ${currentMealBeingViewed.ingredients.join(', ')}
طريقة التحضير: ${currentMealBeingViewed.instructions}
السعرات الحرارية: ${currentMealBeingViewed.calories}`;

                const fullPrompt = `أرغب في تعديل وصفة الطعام التالية:
${mealDetailsForAI}

طلب التعديل: ${userPrompt}

يرجى إعادة كتابة الوصفة بعد التعديل، مع ذكر المكونات وطريقة التحضير والسعرات الحرارية المعدلة (تقديرية). قدم النتيجة بتنسيق واضح وسهل القراءة.`;

                callGeminiAPI(fullPrompt);
            });


            // Initial page load
            showPage('homePage');
        };
    </script>
</body>
</html>
